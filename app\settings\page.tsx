'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import DatabaseManagement from './components/DatabaseManagement';

interface SystemSettings {
  storeName: string;
  orderNumberDigits: number;
  mashangfangxinAppkey: string;
  mashangfangxinAppsecret: string;
  mashangfangxinUrl: string;
  mashangfangxinRefEntId: string;
}

export default function SettingsPage() {
  // 默认设置
  const defaultSettings: SystemSettings = {
    storeName: '药店零售管理系统',
    orderNumberDigits: 4,
    mashangfangxinAppkey: '',
    mashangfangxinAppsecret: '',
    mashangfangxinUrl: 'http://gw.api.taobao.com/router/rest',
    mashangfangxinRefEntId: ''
  };

  const [settings, setSettings] = useState<SystemSettings>({...defaultSettings});
  const [showResetConfirm, setShowResetConfirm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // 获取系统设置
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);

        // 从API获取数据
        try {
          const response = await fetch('/api/settings');
          const data = await response.json();
          if (data.success) {
            // 将API返回的数据转换为SystemSettings格式
            const apiSettings: SystemSettings = {
              storeName: data.data.storeName || defaultSettings.storeName,
              orderNumberDigits: parseInt(data.data.orderNumberDigits) || defaultSettings.orderNumberDigits,
              mashangfangxinAppkey: data.data.mashangfangxinAppkey || '',
              mashangfangxinAppsecret: data.data.mashangfangxinAppsecret || '',
              mashangfangxinUrl: data.data.mashangfangxinUrl || defaultSettings.mashangfangxinUrl,
              mashangfangxinRefEntId: data.data.mashangfangxinRefEntId || ''
            };
            setSettings(apiSettings);
          } else {
            // API调用失败，尝试从本地存储获取
            fallbackToLocalStorage();
          }
        } catch (error) {
          console.error('API调用失败:', error);
          // API调用失败，尝试从本地存储获取
          fallbackToLocalStorage();
        }
      } catch (err) {
        console.error('获取系统设置失败:', err);
        setError('获取系统设置失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    // 从本地存储获取设置的回退方法
    const fallbackToLocalStorage = () => {
      if (typeof window !== 'undefined') {
        const savedSettings = localStorage.getItem('systemSettings');
        if (savedSettings) {
          try {
            const parsedSettings = JSON.parse(savedSettings);
            setSettings({
              ...defaultSettings,
              ...parsedSettings
            });
          } catch (e) {
            console.error('解析设置数据失败:', e);
          }
        }
      }
    };

    fetchSettings();
  }, []);

  // 恢复默认设置
  const resetToDefaults = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // 调用API重置设置
      try {
        const response = await fetch('/api/settings', {
          method: 'DELETE'
        });
        const data = await response.json();

        if (data.success) {
          // 更新本地状态
          setSettings({...defaultSettings});
          setSuccess('已恢复默认设置');

          // 保存到本地存储作为备份
          if (typeof window !== 'undefined') {
            localStorage.setItem('systemSettings', JSON.stringify(defaultSettings));

            // 刷新页面上的其他组件
            const event = new Event('settingsUpdated');
            window.dispatchEvent(event);
          }
        } else {
          setError(data.message || '恢复默认设置失败');
        }
      } catch (error) {
        console.error('API调用失败:', error);
        setError('恢复默认设置失败，请重试');

        // API调用失败，仅更新本地状态和存储
        setSettings({...defaultSettings});
        if (typeof window !== 'undefined') {
          localStorage.setItem('systemSettings', JSON.stringify(defaultSettings));
        }
      }
    } catch (err) {
      console.error('恢复默认设置失败:', err);
      setError('恢复默认设置失败，请重试');
    } finally {
      setSaving(false);
      setShowResetConfirm(false);
    }
  };

  // 保存系统设置
  const saveSettings = async () => {
    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // 验证订单编号位数
      if (settings.orderNumberDigits < 4) {
        setError('订单编号位数不能小于4位');
        setSaving(false);
        return;
      }

      if (settings.orderNumberDigits > 10) {
        setError('订单编号位数不能大于10位');
        setSaving(false);
        return;
      }

      // 验证药店名称
      if (!settings.storeName) {
        setError('药店名称不能为空');
        setSaving(false);
        return;
      }

      // 调用API保存设置
      try {
        const response = await fetch('/api/settings', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(settings),
        });

        const data = await response.json();

        if (data.success) {
          setSuccess('设置保存成功');

          // 保存到本地存储作为备份
          if (typeof window !== 'undefined') {
            localStorage.setItem('systemSettings', JSON.stringify(settings));

            // 刷新页面上的其他组件
            const event = new Event('settingsUpdated');
            window.dispatchEvent(event);
          }
        } else {
          setError(data.message || '保存设置失败');
        }
      } catch (error) {
        console.error('API调用失败:', error);
        setError('保存设置失败，请重试');

        // API调用失败，仅更新本地存储
        if (typeof window !== 'undefined') {
          try {
            localStorage.setItem('systemSettings', JSON.stringify(settings));

            // 刷新页面上的其他组件
            const event = new Event('settingsUpdated');
            window.dispatchEvent(event);
          } catch (e) {
            console.error('保存设置到本地存储失败:', e);
          }
        }
      }
    } catch (err) {
      console.error('保存系统设置失败:', err);
      setError('保存系统设置失败，请重试');
    } finally {
      setSaving(false);
    }
  };

  // 生成订单编号示例
  const generateOrderNumberExample = (prefix = 'SO') => {
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const dateStr = `${year}${month}${day}`;

    // 生成指定位数的序列号
    const sequenceNumber = '1'.padStart(settings.orderNumberDigits, '0');

    return `${prefix}-${dateStr}${sequenceNumber}`;
  };

  return (
    <div className="flex flex-col h-full gap-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-800">系统设置</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* 数据库管理组件 */}
          <DatabaseManagement />

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">基本设置</h2>

            {loading ? (
              <div className="space-y-4">
                <div className="animate-pulse h-10 bg-gray-200 rounded w-full"></div>
                <div className="animate-pulse h-10 bg-gray-200 rounded w-full"></div>
                <div className="animate-pulse h-10 bg-gray-200 rounded w-full"></div>
              </div>
            ) : (
              <div className="space-y-6">
                {/* 药店名称设置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">药店名称</label>
                  <input
                    type="text"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                    value={settings.storeName}
                    onChange={(e) => setSettings({...settings, storeName: e.target.value})}
                    placeholder="请输入药店名称"
                  />
                  <p className="mt-1 text-sm text-gray-500">药店名称将显示在系统界面和打印单据上</p>
                </div>



                {/* 订单编号位数设置 */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">订单编号位数</label>
                  <input
                    type="number"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                    value={settings.orderNumberDigits}
                    onChange={(e) => setSettings({...settings, orderNumberDigits: parseInt(e.target.value) || 4})}
                    min={4}
                    max={10}
                  />
                  <p className="mt-1 text-sm text-gray-500">订单编号日期后的序号位数，建议至少4位，以支持每日大量订单</p>
                </div>

                {/* 码上放心开放平台设置 */}
                <div className="border-t border-gray-200 pt-6 mt-6">
                  <h3 className="text-base font-medium text-gray-900 mb-4">码上放心开放平台设置</h3>

                  <div className="space-y-4">
                    {/* AppKey设置 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">AppKey</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                        value={settings.mashangfangxinAppkey}
                        onChange={(e) => setSettings({...settings, mashangfangxinAppkey: e.target.value})}
                        placeholder="请输入码上放心开放平台AppKey"
                      />
                      <p className="mt-1 text-sm text-gray-500">在码上放心开放平台创建应用后获取的AppKey</p>
                    </div>

                    {/* AppSecret设置 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">AppSecret</label>
                      <input
                        type="password"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                        value={settings.mashangfangxinAppsecret}
                        onChange={(e) => setSettings({...settings, mashangfangxinAppsecret: e.target.value})}
                        placeholder="请输入码上放心开放平台AppSecret"
                      />
                      <p className="mt-1 text-sm text-gray-500">在码上放心开放平台创建应用后获取的AppSecret</p>
                    </div>

                    {/* API URL设置 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">API URL</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                        value={settings.mashangfangxinUrl}
                        onChange={(e) => setSettings({...settings, mashangfangxinUrl: e.target.value})}
                        placeholder="请输入码上放心开放平台API URL"
                      />
                      <p className="mt-1 text-sm text-gray-500">码上放心开放平台API地址，默认为：http://gw.api.taobao.com/router/rest</p>
                    </div>

                    {/* 企业ID设置 */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">企业ID</label>
                      <input
                        type="text"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-blue-700"
                        value={settings.mashangfangxinRefEntId}
                        onChange={(e) => setSettings({...settings, mashangfangxinRefEntId: e.target.value})}
                        placeholder="请输入码上放心开放平台企业ID"
                      />
                      <p className="mt-1 text-sm text-gray-500">码上放心开放平台企业ID，用于药品监管码查询</p>
                    </div>
                  </div>
                </div>

                {/* 订单编号示例 */}
                <div className="p-4 bg-gray-50 rounded-md border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">订单编号示例</h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <span className="text-blue-700 font-mono font-medium w-40">{generateOrderNumberExample('SO')}</span>
                      <span className="text-sm text-gray-500">
                        销售订单 (Sales Order)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-blue-700 font-mono font-medium w-40">{generateOrderNumberExample('RK')}</span>
                      <span className="text-sm text-gray-500">
                        入库单 (Receiving)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-blue-700 font-mono font-medium w-40">{generateOrderNumberExample('CK')}</span>
                      <span className="text-sm text-gray-500">
                        出库单 (Checkout)
                      </span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-blue-700 font-mono font-medium w-40">{generateOrderNumberExample('TH')}</span>
                      <span className="text-sm text-gray-500">
                        退货单 (Return)
                      </span>
                    </div>
                  </div>
                  <p className="mt-3 text-sm text-gray-500">
                    <span className="font-medium">说明：</span>
                    订单编号格式为"前缀-年月日+序号"，前缀根据订单类型自动生成，最后 {settings.orderNumberDigits} 位是当日订单序号
                  </p>
                </div>

                {/* 错误和成功提示 */}
                {error && (
                  <div className="p-3 bg-red-50 border-l-4 border-red-500 text-red-700 rounded">
                    {error}
                  </div>
                )}
                {success && (
                  <div className="p-3 bg-green-50 border-l-4 border-green-500 text-green-700 rounded">
                    {success}
                  </div>
                )}

                {/* 保存和恢复默认按钮 */}
                <div className="flex justify-between">
                  <button
                    onClick={() => setShowResetConfirm(true)}
                    disabled={saving}
                    className={`px-4 py-2 rounded-md text-gray-700 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 ${
                      saving ? 'bg-gray-100 cursor-not-allowed' : 'bg-white hover:bg-gray-50'
                    }`}
                  >
                    恢复默认设置
                  </button>

                  <button
                    onClick={saveSettings}
                    disabled={saving}
                    className={`px-4 py-2 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 ${
                      saving ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {saving ? (
                      <span className="flex items-center">
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        保存中...
                      </span>
                    ) : '保存设置'}
                  </button>
                </div>

                {/* 恢复默认确认对话框 */}
                {showResetConfirm && (
                  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-lg shadow-xl p-6 max-w-md w-full">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">确认恢复默认设置</h3>
                      <p className="text-gray-600 mb-6">
                        您确定要将所有设置恢复为默认值吗？此操作将覆盖您当前的所有自定义设置，且无法撤销。
                      </p>
                      <div className="flex justify-end gap-3">
                        <button
                          onClick={() => setShowResetConfirm(false)}
                          className="px-4 py-2 rounded-md text-gray-700 border border-gray-300 hover:bg-gray-50"
                        >
                          取消
                        </button>
                        <button
                          onClick={resetToDefaults}
                          className="px-4 py-2 rounded-md text-white bg-red-600 hover:bg-red-700"
                        >
                          确认恢复
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">设置说明</h2>
            <div className="space-y-4 text-sm text-gray-600">
              <div>
                <h3 className="font-medium text-gray-700">药店名称</h3>
                <p>药店名称将显示在系统界面顶部和所有打印的单据上，建议使用完整的正式名称。</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-700">订单编号前缀</h3>
                <p>系统根据订单类型自动生成不同的前缀：</p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li><span className="font-medium">SO</span> - 销售订单 (Sales Order)</li>
                  <li><span className="font-medium">RK</span> - 入库单 (Receiving)</li>
                  <li><span className="font-medium">CK</span> - 出库单 (Checkout)</li>
                  <li><span className="font-medium">TH</span> - 退货单 (Return)</li>
                </ul>
              </div>

              <div>
                <h3 className="font-medium text-gray-700">订单编号位数</h3>
                <p>订单编号位数决定了系统每天可以处理的最大订单数量：</p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li><span className="font-medium">4位</span> - 最多9999个订单/天</li>
                  <li><span className="font-medium">5位</span> - 最多99999个订单/天</li>
                  <li><span className="font-medium">6位</span> - 最多999999个订单/天</li>
                </ul>
                <p className="mt-2">对于大多数药店，4位数已经足够（每天最多9999个订单）。如果您的日订单量较大，可以考虑增加位数。</p>
              </div>

              <div>
                <h3 className="font-medium text-gray-700">码上放心开放平台设置</h3>
                <p>码上放心开放平台是阿里健康提供的药品追溯码查询平台，用于获取药品追溯信息。</p>
                <ul className="list-disc pl-5 mt-1 space-y-1">
                  <li><span className="font-medium">AppKey</span> - 在码上放心开放平台创建应用后获取</li>
                  <li><span className="font-medium">AppSecret</span> - 应用密钥，请妥善保管</li>
                  <li><span className="font-medium">API URL</span> - 接口地址，一般无需修改</li>
                  <li><span className="font-medium">企业ID</span> - 药品监管码查询所需的企业ID</li>
                </ul>
                <p className="mt-2">配置完成后，系统将能够通过扫描药品追溯码获取药品详细信息。</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
