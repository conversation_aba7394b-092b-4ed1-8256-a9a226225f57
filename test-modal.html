<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框透明度测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8">模态框透明度测试</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">背景内容</h2>
            <p class="text-gray-600 mb-4">这是页面的背景内容。当模态框打开时，您应该能够透过半透明的黑色遮罩层看到这些内容。</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="bg-blue-100 p-4 rounded">
                    <h3 class="font-medium text-blue-800">测试卡片 1</h3>
                    <p class="text-blue-600">这是一个测试卡片，用于验证透明度效果。</p>
                </div>
                <div class="bg-green-100 p-4 rounded">
                    <h3 class="font-medium text-green-800">测试卡片 2</h3>
                    <p class="text-green-600">这是另一个测试卡片，用于验证透明度效果。</p>
                </div>
            </div>
        </div>

        <div class="space-x-4">
            <button 
                onclick="showModal1()" 
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
                测试模态框 1 (Tailwind CSS)
            </button>
            <button 
                onclick="showModal2()" 
                class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
            >
                测试模态框 2 (内联样式)
            </button>
        </div>
    </div>

    <!-- 模态框 1 - 使用 Tailwind CSS -->
    <div id="modal1" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 class="text-lg font-medium text-blue-700 mb-4">测试模态框 1</h3>
            <p class="text-gray-600 mb-4">这个模态框使用 Tailwind CSS 的 bg-black bg-opacity-50 类来创建半透明背景。</p>
            <button onclick="hideModal1()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                关闭
            </button>
        </div>
    </div>

    <!-- 模态框 2 - 使用内联样式 -->
    <div id="modal2" class="hidden fixed inset-0 flex items-center justify-center z-50 p-4" style="background-color: rgba(0, 0, 0, 0.5);">
        <div class="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h3 class="text-lg font-medium text-green-700 mb-4">测试模态框 2</h3>
            <p class="text-gray-600 mb-4">这个模态框使用内联样式 background-color: rgba(0, 0, 0, 0.5) 来创建半透明背景。</p>
            <button onclick="hideModal2()" class="px-4 py-2 bg-gray-500 text-white rounded-md hover:bg-gray-600">
                关闭
            </button>
        </div>
    </div>

    <script>
        function showModal1() {
            document.getElementById('modal1').classList.remove('hidden');
        }

        function hideModal1() {
            document.getElementById('modal1').classList.add('hidden');
        }

        function showModal2() {
            document.getElementById('modal2').classList.remove('hidden');
        }

        function hideModal2() {
            document.getElementById('modal2').classList.add('hidden');
        }

        // 点击遮罩层关闭模态框
        document.getElementById('modal1').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal1();
            }
        });

        document.getElementById('modal2').addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal2();
            }
        });
    </script>
</body>
</html>
