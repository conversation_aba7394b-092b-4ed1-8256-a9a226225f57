'use client';

import Link from "next/link";
import { useState, useEffect, use } from "react";
import { useRouter } from "next/navigation";
import ErrorModal from '@/app/components/ErrorModal';
import SuccessModal from '@/app/components/SuccessModal';

// 订单状态中英文映射
const statusMap: { [key: string]: string } = {
  'pending': '待处理',
  'processing': '处理中',
  'completed': '已完成',
  'cancelled': '已取消',
  'refunded': '已退款'
};

// 支付方式中英文映射
const paymentMethodMap: { [key: string]: string } = {
  'cash': '现金',
  'wechat': '微信支付',
  'alipay': '支付宝',
  'card': '银行卡',
  'other': '其他'
};

interface OrderDetail {
  id: string;
  orderNumber: string;
  date: string;
  status: string;
  customer: {
    name: string;
    phone: string;
    isMember: boolean;
    memberNumber?: string;
  } | null;
  items: Array<{
    id: string;
    name: string;
    specification: string;
    price: number;
    quantity: number;
    subtotal: number;
  }>;
  payment: {
    method: string;
    amount: number;
    time: string;
  };
  note: string;
  total: number;
  discount: number;
  staff: string;
  createdAt: string;
}

export default function OrderDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [isPrinting, setIsPrinting] = useState(false);
  const [order, setOrder] = useState<OrderDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // 新增状态
  const [showEditModal, setShowEditModal] = useState(false);
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [storeName, setStoreName] = useState('药店零售管理系统');
  const [receiptSize, setReceiptSize] = useState<'58mm' | '80mm' | '112mm'>('80mm');

  // 弹窗状态
  const [showErrorModal, setShowErrorModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [successMessage, setSuccessMessage] = useState('');

  // 显示错误弹窗
  const showError = (message: string) => {
    setErrorMessage(message);
    setShowErrorModal(true);
  };

  // 显示成功弹窗
  const showSuccess = (message: string) => {
    setSuccessMessage(message);
    setShowSuccessModal(true);
  };

  // 获取系统设置
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/settings');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            if (data.data.storeName) {
              setStoreName(data.data.storeName);
            }
            if (data.data.receiptSize) {
              setReceiptSize(data.data.receiptSize as '58mm' | '80mm' | '112mm');
            }
          }
        }
      } catch (error) {
        console.error('获取系统设置失败:', error);
      }
    };

    fetchSettings();
  }, []);

  // 获取订单详情
  useEffect(() => {
    const fetchOrderDetail = async () => {
      try {
        setLoading(true);
        setError('');

        const response = await fetch(`/api/orders/${resolvedParams.id}`);

        if (!response.ok) {
          throw new Error(`获取订单详情失败: ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
          setOrder(data.data);
        } else {
          setError(data.message || '获取订单详情失败');
        }
      } catch (err) {
        console.error('获取订单详情失败:', err);
        setError('获取订单详情失败，请刷新页面重试');
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetail();
  }, [resolvedParams.id]);

  // 触发打印
  const handlePrint = () => {
    setIsPrinting(true);

    // 创建打印内容
    const printContent = generatePrintContent();

    // 创建新窗口进行打印
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.focus();

      // 等待内容加载完成后打印
      setTimeout(() => {
        printWindow.print();
        printWindow.close();
        setIsPrinting(false);
      }, 500);
    } else {
      setIsPrinting(false);
    }
  };

  // 生成打印内容
  const generatePrintContent = () => {
    if (!order) return '';

    const currentDate = new Date().toLocaleString('zh-CN');

    // 根据小票尺寸设置样式
    const sizeConfig = {
      '58mm': {
        width: '58mm',
        fontSize: '10px',
        padding: '8px',
        lineHeight: '1.2'
      },
      '80mm': {
        width: '80mm',
        fontSize: '12px',
        padding: '12px',
        lineHeight: '1.4'
      },
      '112mm': {
        width: '112mm',
        fontSize: '14px',
        padding: '16px',
        lineHeight: '1.5'
      }
    };

    const config = sizeConfig[receiptSize];

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>订单详情 - ${order.orderNumber}</title>
        <style>
          * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
          }

          body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            font-size: ${config.fontSize};
            line-height: ${config.lineHeight};
            color: #333;
            padding: ${config.padding};
            width: ${config.width};
            margin: 0 auto;
          }

          .receipt {
            width: 100%;
            margin: 0 auto;
          }

          .header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: ${receiptSize === '58mm' ? '6px' : receiptSize === '80mm' ? '8px' : '10px'};
            margin-bottom: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '12px' : '15px'};
          }

          .store-name {
            font-size: ${receiptSize === '58mm' ? '14px' : receiptSize === '80mm' ? '16px' : '18px'};
            font-weight: bold;
            margin-bottom: ${receiptSize === '58mm' ? '3px' : '5px'};
          }

          .receipt-title {
            font-size: ${receiptSize === '58mm' ? '12px' : receiptSize === '80mm' ? '14px' : '16px'};
            font-weight: bold;
            margin-bottom: ${receiptSize === '58mm' ? '3px' : '5px'};
          }

          .order-info {
            margin-bottom: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '12px' : '15px'};
          }

          .info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: ${receiptSize === '58mm' ? '2px' : '3px'};
            ${receiptSize === '58mm' ? 'font-size: 9px;' : ''}
          }

          .customer-info {
            margin-bottom: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '12px' : '15px'};
            padding: ${receiptSize === '58mm' ? '4px' : receiptSize === '80mm' ? '6px' : '8px'};
            background-color: #f5f5f5;
          }

          .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '12px' : '15px'};
            ${receiptSize === '58mm' ? 'font-size: 9px;' : ''}
          }

          .items-table th,
          .items-table td {
            padding: ${receiptSize === '58mm' ? '2px' : receiptSize === '80mm' ? '3px' : '5px'};
            text-align: left;
            border-bottom: 1px solid #ddd;
            ${receiptSize === '58mm' ? 'word-break: break-all;' : ''}
          }

          .items-table th {
            background-color: #f0f0f0;
            font-weight: bold;
          }

          .items-table .item-name {
            ${receiptSize === '58mm' ? 'max-width: 60px;' : receiptSize === '80mm' ? 'max-width: 80px;' : 'max-width: 120px;'}
          }

          .total-section {
            border-top: 2px solid #333;
            padding-top: ${receiptSize === '58mm' ? '6px' : receiptSize === '80mm' ? '8px' : '10px'};
            margin-bottom: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '12px' : '15px'};
          }

          .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: ${receiptSize === '58mm' ? '2px' : '3px'};
            ${receiptSize === '58mm' ? 'font-size: 9px;' : ''}
          }

          .final-total {
            font-size: ${receiptSize === '58mm' ? '11px' : receiptSize === '80mm' ? '13px' : '14px'};
            font-weight: bold;
            border-top: 1px solid #333;
            padding-top: ${receiptSize === '58mm' ? '3px' : '5px'};
            margin-top: ${receiptSize === '58mm' ? '3px' : '5px'};
          }

          .payment-info {
            margin-bottom: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '12px' : '15px'};
            padding: ${receiptSize === '58mm' ? '4px' : receiptSize === '80mm' ? '6px' : '8px'};
            background-color: #f5f5f5;
          }

          .footer {
            text-align: center;
            border-top: 1px solid #333;
            padding-top: ${receiptSize === '58mm' ? '6px' : receiptSize === '80mm' ? '8px' : '10px'};
            font-size: ${receiptSize === '58mm' ? '8px' : receiptSize === '80mm' ? '9px' : '10px'};
            color: #666;
          }

          .note {
            margin-bottom: ${receiptSize === '58mm' ? '6px' : receiptSize === '80mm' ? '8px' : '10px'};
            padding: ${receiptSize === '58mm' ? '3px' : '5px'};
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            ${receiptSize === '58mm' ? 'font-size: 9px;' : ''}
          }

          @media print {
            body {
              padding: 0;
              width: ${config.width};
              margin: 0;
            }

            .receipt {
              width: 100%;
            }

            @page {
              size: ${config.width} auto;
              margin: 0;
            }
          }
        </style>
      </head>
      <body>
        <div class="receipt">
          <div class="header">
            <div class="store-name">${storeName}</div>
            <div class="receipt-title">销售小票</div>
            <div>打印时间: ${currentDate}</div>
          </div>

          <div class="order-info">
            <div class="info-row">
              <span>订单编号:</span>
              <span>${order.orderNumber}</span>
            </div>
            <div class="info-row">
              <span>订单日期:</span>
              <span>${order.createdAt}</span>
            </div>
            <div class="info-row">
              <span>操作员:</span>
              <span>${order.staff}</span>
            </div>
          </div>

          ${order.customer ? `
          <div class="customer-info">
            <div><strong>客户信息</strong></div>
            <div class="info-row">
              <span>姓名:</span>
              <span>${order.customer.name}</span>
            </div>
            <div class="info-row">
              <span>电话:</span>
              <span>${order.customer.phone || '未提供'}</span>
            </div>
            ${order.customer.isMember && order.customer.memberNumber ? `
            <div class="info-row">
              <span>会员编号:</span>
              <span>${order.customer.memberNumber}</span>
            </div>
            ` : ''}
          </div>
          ` : ''}

          <table class="items-table">
            <thead>
              <tr>
                ${receiptSize === '58mm' ? `
                  <th class="item-name">药品</th>
                  <th>价格</th>
                  <th>数量</th>
                  <th>小计</th>
                ` : `
                  <th class="item-name">药品名称</th>
                  <th>单价</th>
                  <th>数量</th>
                  <th>小计</th>
                `}
              </tr>
            </thead>
            <tbody>
              ${order.items.map(item => `
                <tr>
                  <td class="item-name">
                    ${receiptSize === '58mm' ?
                      `${item.name.length > 8 ? item.name.substring(0, 8) + '...' : item.name}` :
                      `${item.name}<br><small>${item.specification}</small>`
                    }
                  </td>
                  <td>¥${item.price.toFixed(2)}</td>
                  <td>${item.quantity}</td>
                  <td>¥${item.subtotal.toFixed(2)}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>

          <div class="total-section">
            <div class="total-row">
              <span>商品总数:</span>
              <span>${order.items.reduce((sum, item) => sum + item.quantity, 0)} 件</span>
            </div>
            <div class="total-row">
              <span>商品金额:</span>
              <span>¥${order.total.toFixed(2)}</span>
            </div>
            <div class="total-row">
              <span>优惠金额:</span>
              <span>-¥${order.discount.toFixed(2)}</span>
            </div>
            <div class="total-row final-total">
              <span>应付金额:</span>
              <span>¥${(order.total - order.discount).toFixed(2)}</span>
            </div>
          </div>

          <div class="payment-info">
            <div><strong>支付信息</strong></div>
            <div class="info-row">
              <span>支付方式:</span>
              <span>${paymentMethodMap[order.payment.method] || order.payment.method}</span>
            </div>
            <div class="info-row">
              <span>收款金额:</span>
              <span>¥${order.payment.amount.toFixed(2)}</span>
            </div>
            <div class="info-row">
              <span>找零金额:</span>
              <span>¥${(order.payment.amount - (order.total - order.discount)).toFixed(2)}</span>
            </div>
            <div class="info-row">
              <span>支付时间:</span>
              <span>${order.payment.time}</span>
            </div>
          </div>

          ${order.note ? `
          <div class="note">
            <strong>备注:</strong> ${order.note}
          </div>
          ` : ''}

          <div class="footer">
            <div>谢谢惠顾，欢迎再次光临！</div>
            <div>如有问题请及时联系我们</div>
          </div>
        </div>
      </body>
      </html>
    `;
  };

  // 处理修改订单
  const handleEditOrder = () => {
    setShowEditModal(true);
  };

  // 处理退款
  const handleRefund = () => {
    setShowRefundModal(true);
  };

  // 订单编辑表单组件
  const EditOrderForm = ({ order, onSave, onCancel }: {
    order: OrderDetail | null;
    onSave: () => void;
    onCancel: () => void;
  }) => {
    const [editData, setEditData] = useState({
      customerName: order?.customer?.name || '',
      customerPhone: order?.customer?.phone || '',
      isMember: order?.customer?.isMember || false,
      memberNumber: order?.customer?.memberNumber || '',
      note: order?.note || '',
      discountAmount: order?.discount || 0,
      items: order?.items || []
    });

    const [isSubmitting, setIsSubmitting] = useState(false);

    const handleSave = async () => {
      setIsSubmitting(true);
      try {
        const response = await fetch(`/api/orders/${order?.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            customerName: editData.customerName,
            customerPhone: editData.customerPhone,
            isMember: editData.isMember,
            memberNumber: editData.memberNumber,
            note: editData.note,
            discountAmount: editData.discountAmount,
            items: editData.items
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            showSuccess('订单修改成功！');
            // 延迟关闭弹窗并重新加载数据
            setTimeout(() => {
              setShowSuccessModal(false);
              window.location.reload();
              onSave();
            }, 1500);
          } else {
            showError('修改失败：' + (data.message || '未知错误'));
          }
        } else {
          const errorData = await response.json().catch(() => ({}));
          showError('修改失败：' + (errorData.message || '请重试'));
        }
      } catch (error) {
        console.error('修改订单失败:', error);
        showError('修改失败：网络错误，请检查网络连接后重试');
      } finally {
        setIsSubmitting(false);
      }
    };

    const updateItemQuantity = (index: number, newQuantity: number) => {
      if (newQuantity <= 0) return;

      const newItems = [...editData.items];
      newItems[index] = {
        ...newItems[index],
        quantity: newQuantity,
        subtotal: newItems[index].price * newQuantity
      };
      setEditData({ ...editData, items: newItems });
    };

    const removeItem = (index: number) => {
      const newItems = editData.items.filter((_, i) => i !== index);
      setEditData({ ...editData, items: newItems });
    };

    const totalAmount = editData.items.reduce((sum, item) => sum + item.subtotal, 0);
    const finalAmount = totalAmount - editData.discountAmount;

    return (
      <div className="space-y-6">
        {/* 客户信息编辑 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-700 mb-4">客户信息</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">客户姓名</label>
              <input
                type="text"
                value={editData.customerName}
                onChange={(e) => setEditData({ ...editData, customerName: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                placeholder="散客"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-blue-700 mb-1">联系电话</label>
              <input
                type="text"
                value={editData.customerPhone}
                onChange={(e) => setEditData({ ...editData, customerPhone: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                placeholder="未提供"
              />
            </div>
            <div className="md:col-span-2">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={editData.isMember}
                  onChange={(e) => setEditData({ ...editData, isMember: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm font-medium text-blue-700">会员客户</span>
              </label>
              {editData.isMember && (
                <input
                  type="text"
                  value={editData.memberNumber}
                  onChange={(e) => setEditData({ ...editData, memberNumber: e.target.value })}
                  className="mt-2 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                  placeholder="会员编号"
                />
              )}
            </div>
          </div>
        </div>

        {/* 药品清单编辑 */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-700 mb-4">药品清单</h4>
          <div className="space-y-3">
            {editData.items.map((item, index) => (
              <div key={index} className="bg-white p-3 rounded border flex items-center justify-between">
                <div className="flex-1">
                  <div className="font-medium text-blue-700">{item.name}</div>
                  <div className="text-sm text-blue-600">{item.specification}</div>
                  <div className="text-sm text-blue-600">单价: ¥{item.price.toFixed(2)}</div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => updateItemQuantity(index, item.quantity - 1)}
                      className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center hover:bg-gray-300 text-blue-700 font-medium"
                    >
                      -
                    </button>
                    <span className="w-12 text-center font-medium text-blue-700">{item.quantity}</span>
                    <button
                      onClick={() => updateItemQuantity(index, item.quantity + 1)}
                      className="w-8 h-8 bg-gray-200 rounded flex items-center justify-center hover:bg-gray-300 text-blue-700 font-medium"
                    >
                      +
                    </button>
                  </div>
                  <div className="text-blue-700 font-semibold w-20 text-right">
                    ¥{item.subtotal.toFixed(2)}
                  </div>
                  <button
                    onClick={() => removeItem(index)}
                    className="text-red-600 hover:text-red-800 p-1"
                  >
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 优惠和备注 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">优惠金额</label>
            <input
              type="number"
              value={editData.discountAmount}
              onChange={(e) => setEditData({ ...editData, discountAmount: parseFloat(e.target.value) || 0 })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
              min="0"
              step="0.01"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-blue-700 mb-1">备注信息</label>
            <input
              type="text"
              value={editData.note}
              onChange={(e) => setEditData({ ...editData, note: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
              placeholder="订单备注"
            />
          </div>
        </div>

        {/* 金额汇总 */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-gray-700 font-medium">商品总额:</span>
              <span className="text-blue-900 font-bold">¥{totalAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-700 font-medium">优惠金额:</span>
              <span className="text-red-700 font-bold">-¥{editData.discountAmount.toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-lg font-bold border-t pt-2">
              <span className="text-gray-800 font-bold">应付金额:</span>
              <span className="text-blue-900 text-xl font-black">¥{finalAmount.toFixed(2)}</span>
            </div>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 pt-4 border-t">
          <button
            onClick={onCancel}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
          >
            {isSubmitting ? '保存中...' : '保存修改'}
          </button>
        </div>
      </div>
    );
  };

  // 加载状态
  if (loading) {
    return (
      <div className="flex flex-col h-full gap-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Link href="/sales" className="text-blue-600 hover:text-blue-800">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </Link>
            <h1 className="text-2xl font-bold text-gray-800">订单详情</h1>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-500">加载订单详情中...</p>
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <div className="flex flex-col h-full gap-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Link href="/sales" className="text-blue-600 hover:text-blue-800">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </Link>
            <h1 className="text-2xl font-bold text-gray-800">订单详情</h1>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <p className="text-red-500 text-lg">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 订单不存在
  if (!order) {
    return (
      <div className="flex flex-col h-full gap-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Link href="/sales" className="text-blue-600 hover:text-blue-800">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
              </svg>
            </Link>
            <h1 className="text-2xl font-bold text-gray-800">订单详情</h1>
          </div>
        </div>
        <div className="flex justify-center items-center h-64">
          <div className="text-center">
            <div className="text-gray-400 text-6xl mb-4">📋</div>
            <p className="text-gray-500 text-lg">订单不存在</p>
            <Link href="/sales" className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
              返回销售管理
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full gap-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Link href="/sales" className="text-blue-600 hover:text-blue-800">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
          </Link>
          <h1 className="text-2xl font-bold text-gray-800">订单详情</h1>
          <span className="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 ml-2">
            {statusMap[order.status] || order.status}
          </span>
        </div>
        <div className="flex gap-3 items-center">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-600">小票尺寸: <span className="text-blue-700 font-medium">{receiptSize}</span></span>
          </div>
          <button
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition flex items-center gap-1"
            onClick={handlePrint}
            disabled={isPrinting}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
            </svg>
            {isPrinting ? '打印中...' : '打印订单'}
          </button>
          <button
            onClick={handleEditOrder}
            className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition flex items-center gap-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            修改订单
          </button>
          <button
            onClick={handleRefund}
            className="px-4 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition flex items-center gap-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m5 14-5-2a2 2 0 00-2 0l-5 2v-7a2 2 0 012-2h8a2 2 0 012 2v7z" />
            </svg>
            申请退款
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex justify-between items-start mb-6">
              <div>
                <h2 className="text-lg font-medium text-gray-800">订单信息</h2>
                <p className="text-sm text-blue-600 mt-1 font-medium">订单号: <span className="text-blue-700 font-semibold">{order.orderNumber}</span></p>
              </div>
              <div className="text-right">
                <p className="text-sm text-gray-500">创建时间: <span className="text-blue-600">{order.createdAt}</span></p>
                <p className="text-sm text-gray-500 mt-1">操作员: <span className="text-blue-600">{order.staff}</span></p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <h3 className="text-md font-medium text-gray-800 mb-2">客户信息</h3>
                <div className="space-y-1">
                  <p className="text-sm"><span className="text-gray-500">姓名:</span> <span className="text-blue-700 font-medium">{order.customer?.name || '散客'}</span></p>
                  <p className="text-sm"><span className="text-gray-500">电话:</span> <span className="text-blue-600">{order.customer?.phone || '未提供'}</span></p>
                  {order.customer?.isMember && order.customer?.memberNumber && (
                    <p className="text-sm"><span className="text-gray-500">会员编号:</span> <span className="text-blue-600 font-medium">{order.customer.memberNumber}</span></p>
                  )}
                </div>
              </div>
              <div>
                <h3 className="text-md font-medium text-gray-800 mb-2">支付信息</h3>
                <div className="space-y-1">
                  <p className="text-sm"><span className="text-gray-500">支付方式:</span> <span className="text-blue-700 font-medium">{paymentMethodMap[order.payment.method] || order.payment.method}</span></p>
                  <p className="text-sm"><span className="text-gray-500">支付金额:</span> <span className="text-blue-700 font-semibold">¥{order.payment.amount.toFixed(2)}</span></p>
                  <p className="text-sm"><span className="text-gray-500">支付时间:</span> <span className="text-blue-600">{order.payment.time}</span></p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-md font-medium text-gray-800 mb-3">药品清单</h3>
              <div className="overflow-x-auto border border-gray-300 rounded-md mb-4">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">药品名称</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">规格</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">单价</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数量</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">小计</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {order.items.map((item, index) => (
                      <tr key={index}>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-medium">{item.name}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">{item.specification}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-semibold">¥{item.price.toFixed(2)}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-600 font-medium">{item.quantity}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-blue-700 font-bold">¥{item.subtotal.toFixed(2)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {order.note && (
                <div className="flex gap-4 items-center text-sm mb-3">
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-2 flex items-center gap-1 text-blue-700 w-full">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>备注：{order.note}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 className="text-lg font-medium text-gray-800 mb-4">结算汇总</h2>

            <div className="space-y-3 mb-6">
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">商品总数:</span>
                <span className="font-medium text-blue-700">{order.items.reduce((sum, item) => sum + item.quantity, 0)} 件</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">商品金额:</span>
                <span className="font-medium text-blue-700">¥{order.total.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">优惠金额:</span>
                <span className="font-medium text-red-600">-¥{order.discount.toFixed(2)}</span>
              </div>
              <div className="pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-gray-800 font-medium">应付金额:</span>
                  <span className="text-xl font-bold text-blue-600">¥{(order.total - order.discount).toFixed(2)}</span>
                </div>
              </div>
              <div className="flex justify-between items-center text-sm pt-3">
                <span className="text-gray-600">收款金额:</span>
                <span className="font-medium text-blue-700">¥{order.payment.amount.toFixed(2)}</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span className="text-gray-600">找零金额:</span>
                <span className="font-medium text-blue-600">¥{(order.payment.amount - (order.total - order.discount)).toFixed(2)}</span>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-md font-medium text-gray-800">订单状态跟踪</h3>
              <div className="border-l-2 border-blue-500 pl-4 py-2 space-y-6">
                <div className="relative">
                  <div className="absolute -left-6 top-1 w-4 h-4 rounded-full bg-blue-500"></div>
                  <p className="text-sm font-medium text-blue-700">订单创建</p>
                  <p className="text-xs text-blue-500">{order.createdAt}</p>
                </div>
                <div className="relative">
                  <div className="absolute -left-6 top-1 w-4 h-4 rounded-full bg-blue-500"></div>
                  <p className="text-sm font-medium text-blue-700">支付完成</p>
                  <p className="text-xs text-blue-500">{order.payment.time}</p>
                </div>
                <div className="relative">
                  <div className="absolute -left-6 top-1 w-4 h-4 rounded-full bg-green-500"></div>
                  <p className="text-sm font-medium text-green-700">订单完成</p>
                  <p className="text-xs text-green-500">{order.payment.time}</p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 className="text-md font-medium text-gray-800 mb-3">快捷操作</h3>
            <div className="space-y-3">
              <button
                onClick={handlePrint}
                disabled={isPrinting}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 transition"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                打印订单
              </button>
              <button
                onClick={handleRefund}
                className="w-full flex items-center justify-center gap-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                退换/退款
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 修改订单模态框 */}
      {showEditModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.5)'
          }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowEditModal(false);
            }
          }}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-lg font-medium text-blue-700">修改订单 - {order?.orderNumber}</h3>
                  </div>
                </div>
                <button
                  onClick={() => setShowEditModal(false)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              {/* 订单编辑表单 */}
              <EditOrderForm order={order} onSave={() => setShowEditModal(false)} onCancel={() => setShowEditModal(false)} />
            </div>
          </div>
        </div>
      )}

      {/* 退款模态框 */}
      {showRefundModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4"
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.5)'
          }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowRefundModal(false);
            }
          }}
        >
          <div
            className="bg-white rounded-lg shadow-xl max-w-lg w-full max-h-[90vh] overflow-y-auto"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="p-6">
              <div className="flex items-center mb-4">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m5 14-5-2a2 2 0 00-2 0l-5 2v-7a2 2 0 012-2h8a2 2 0 012 2v7z" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-medium text-blue-700">申请退款</h3>
                </div>
              </div>

              <div className="mb-6">
                <div className="bg-gray-50 p-4 rounded-lg mb-4">
                  <h4 className="font-medium text-blue-700 mb-2">订单信息</h4>
                  <div className="text-sm text-blue-600 space-y-1">
                    <div>订单编号: <span className="text-blue-700 font-medium">{order?.orderNumber}</span></div>
                    <div>订单金额: <span className="text-blue-700 font-medium">¥{order ? (order.total - order.discount).toFixed(2) : '0.00'}</span></div>
                    <div>支付方式: <span className="text-blue-700 font-medium">{order ? (paymentMethodMap[order.payment.method] || order.payment.method) : ''}</span></div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-2">退款类型</label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input type="radio" name="refundType" value="full" defaultChecked className="mr-2" />
                        <span className="text-sm text-blue-700">全额退款</span>
                      </label>
                      <label className="flex items-center">
                        <input type="radio" name="refundType" value="partial" className="mr-2" />
                        <span className="text-sm text-blue-700">部分退款</span>
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-2">退款原因</label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700">
                      <option value="">请选择退款原因</option>
                      <option value="quality">药品质量问题</option>
                      <option value="wrong">购买错误</option>
                      <option value="expired">药品过期</option>
                      <option value="customer">客户要求</option>
                      <option value="other">其他原因</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-blue-700 mb-2">备注说明</label>
                    <textarea
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-blue-700"
                      rows={3}
                      placeholder="请详细说明退款原因..."
                    ></textarea>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowRefundModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  取消
                </button>
                <button
                  onClick={() => {
                    // 这里可以添加退款处理逻辑
                    showSuccess('退款申请已提交，请等待处理');
                    setTimeout(() => {
                      setShowSuccessModal(false);
                      setShowRefundModal(false);
                    }, 1500);
                  }}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  提交申请
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 错误弹窗 */}
      <ErrorModal
        isOpen={showErrorModal}
        onClose={() => setShowErrorModal(false)}
        message={errorMessage}
      />

      {/* 成功弹窗 */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        message={successMessage}
      />

      {/* 打印样式，在打印时显示 */}
      <style jsx global>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .bg-white, .bg-white * {
            visibility: visible;
          }
          .bg-white {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
          }
          button, .text-blue-600, svg {
            display: none !important;
          }
        }
      `}</style>
    </div>
  );
}