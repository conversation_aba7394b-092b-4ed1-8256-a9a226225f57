import { NextRequest, NextResponse } from 'next/server';
import { query, get } from '@/lib/db';

// 获取单个订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const orderId = resolvedParams.id;

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: '订单ID不能为空' },
        { status: 400 }
      );
    }

    // 获取订单基本信息
    const order = await get(`
      SELECT
        o.id as id,
        o.order_number as orderNumber,
        o.total_amount as total,
        o.discount_amount as discount,
        o.payable_amount as payableAmount,
        o.received_amount as receivedAmount,
        o.change_amount as changeAmount,
        o.payment_method as paymentMethod,
        o.status as status,
        o.note as note,
        o.created_at as createdAt,
        c.name as customerName,
        c.phone as customerPhone,
        c.is_member as customerIsMember,
        c.member_number as customerMemberNumber
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.id = ?
    `, [orderId]);

    if (!order) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 获取订单明细
    const orderItems = await query(`
      SELECT
        od.id as id,
        od.quantity as quantity,
        od.unit_price as price,
        od.subtotal as subtotal,
        p.name as name,
        p.specification as specification
      FROM order_details od
      JOIN products p ON od.product_id = p.id
      WHERE od.order_id = ?
      ORDER BY od.id ASC
    `, [orderId]);

    // 格式化订单数据
    const orderDetail = {
      id: order.id.toString(),
      orderNumber: order.orderNumber,
      date: order.createdAt.split(' ')[0], // 只取日期部分
      status: order.status,
      customer: order.customerName ? {
        name: order.customerName,
        phone: order.customerPhone || '',
        isMember: order.customerIsMember === 1,
        memberNumber: order.customerMemberNumber || ''
      } : null,
      items: orderItems.map((item: any) => ({
        id: item.id.toString(),
        name: item.name,
        specification: item.specification || '',
        price: parseFloat(item.price),
        quantity: parseInt(item.quantity),
        subtotal: parseFloat(item.subtotal)
      })),
      payment: {
        method: order.paymentMethod,
        amount: parseFloat(order.receivedAmount || order.payableAmount),
        time: order.createdAt
      },
      note: order.note || '',
      total: parseFloat(order.total),
      discount: parseFloat(order.discount || 0),
      staff: '系统管理员', // 暂时固定，后续可以从用户表获取
      createdAt: order.createdAt
    };

    return NextResponse.json({
      success: true,
      data: orderDetail
    });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    return NextResponse.json(
      { success: false, message: '获取订单详情失败' },
      { status: 500 }
    );
  }
}
