import { NextRequest, NextResponse } from 'next/server';
import { query, get, run } from '@/lib/db';

// 获取单个订单详情
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const orderId = resolvedParams.id;

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: '订单ID不能为空' },
        { status: 400 }
      );
    }

    // 获取订单基本信息
    const order = await get(`
      SELECT
        o.id as id,
        o.order_number as orderNumber,
        o.total_amount as total,
        o.discount_amount as discount,
        o.payable_amount as payableAmount,
        o.received_amount as receivedAmount,
        o.change_amount as changeAmount,
        o.payment_method as paymentMethod,
        o.status as status,
        o.note as note,
        o.created_at as createdAt,
        c.name as customerName,
        c.phone as customerPhone,
        c.is_member as customerIsMember,
        c.member_number as customerMemberNumber
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE o.id = ?
    `, [orderId]);

    if (!order) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 获取订单明细
    const orderItems = await query(`
      SELECT
        od.id as id,
        od.quantity as quantity,
        od.unit_price as price,
        od.subtotal as subtotal,
        p.name as name,
        p.specification as specification
      FROM order_details od
      JOIN products p ON od.product_id = p.id
      WHERE od.order_id = ?
      ORDER BY od.id ASC
    `, [orderId]);

    // 格式化订单数据
    const orderDetail = {
      id: order.id.toString(),
      orderNumber: order.orderNumber,
      date: order.createdAt.split(' ')[0], // 只取日期部分
      status: order.status,
      customer: order.customerName ? {
        name: order.customerName,
        phone: order.customerPhone || '',
        isMember: order.customerIsMember === 1,
        memberNumber: order.customerMemberNumber || ''
      } : null,
      items: orderItems.map((item: any) => ({
        id: item.id.toString(),
        name: item.name,
        specification: item.specification || '',
        price: parseFloat(item.price),
        quantity: parseInt(item.quantity),
        subtotal: parseFloat(item.subtotal)
      })),
      payment: {
        method: order.paymentMethod,
        amount: parseFloat(order.receivedAmount || order.payableAmount),
        time: order.createdAt
      },
      note: order.note || '',
      total: parseFloat(order.total),
      discount: parseFloat(order.discount || 0),
      staff: '系统管理员', // 暂时固定，后续可以从用户表获取
      createdAt: order.createdAt
    };

    return NextResponse.json({
      success: true,
      data: orderDetail
    });
  } catch (error) {
    console.error('获取订单详情失败:', error);
    return NextResponse.json(
      { success: false, message: '获取订单详情失败' },
      { status: 500 }
    );
  }
}

// 修改订单
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const orderId = resolvedParams.id;
    const data = await request.json();

    if (!orderId) {
      return NextResponse.json(
        { success: false, message: '订单ID不能为空' },
        { status: 400 }
      );
    }

    // 验证订单是否存在
    const existingOrder = await get(
      'SELECT id, status FROM orders WHERE id = ?',
      [orderId]
    );

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, message: '订单不存在' },
        { status: 404 }
      );
    }

    // 检查订单状态是否允许修改
    if (existingOrder.status === 'cancelled' || existingOrder.status === 'refunded') {
      return NextResponse.json(
        { success: false, message: '已取消或已退款的订单不能修改' },
        { status: 400 }
      );
    }

    const {
      customerName,
      customerPhone,
      isMember,
      memberNumber,
      note,
      discountAmount,
      items
    } = data;

    // 开始事务
    await run('BEGIN TRANSACTION');

    try {
      // 1. 更新或创建客户信息
      let customerId = null;
      if (customerName && customerName !== '散客') {
        // 检查客户是否已存在
        const existingCustomer = await get(
          'SELECT id FROM customers WHERE name = ? AND phone = ?',
          [customerName, customerPhone || '']
        );

        if (existingCustomer) {
          customerId = existingCustomer.id;
          // 更新客户信息
          await run(
            `UPDATE customers SET
             is_member = ?, member_number = ?, updated_at = CURRENT_TIMESTAMP
             WHERE id = ?`,
            [isMember ? 1 : 0, memberNumber || '', customerId]
          );
        } else {
          // 创建新客户
          const customerResult = await run(
            `INSERT INTO customers (name, phone, is_member, member_number)
             VALUES (?, ?, ?, ?)`,
            [customerName, customerPhone || '', isMember ? 1 : 0, memberNumber || '']
          );
          customerId = customerResult.lastID;
        }
      }

      // 2. 计算新的金额
      const totalAmount = items.reduce((sum: number, item: any) => sum + (item.price * item.quantity), 0);
      const payableAmount = totalAmount - (discountAmount || 0);

      // 3. 更新订单基本信息
      await run(
        `UPDATE orders SET
         customer_id = ?, total_amount = ?, discount_amount = ?,
         payable_amount = ?, note = ?, updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [customerId, totalAmount, discountAmount || 0, payableAmount, note || '', orderId]
      );

      // 4. 删除原有订单明细
      await run('DELETE FROM order_items WHERE order_id = ?', [orderId]);

      // 5. 插入新的订单明细
      for (const item of items) {
        await run(
          `INSERT INTO order_items (order_id, product_id, product_name, specification, price, quantity, subtotal)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
          [
            orderId,
            item.id,
            item.name,
            item.specification || '',
            item.price,
            item.quantity,
            item.price * item.quantity
          ]
        );
      }

      // 提交事务
      await run('COMMIT');

      return NextResponse.json({
        success: true,
        message: '订单修改成功'
      });

    } catch (error) {
      // 回滚事务
      await run('ROLLBACK');
      throw error;
    }

  } catch (error) {
    console.error('修改订单失败:', error);
    return NextResponse.json(
      {
        success: false,
        message: '修改订单失败',
        error: error instanceof Error ? error.message : '未知错误'
      },
      { status: 500 }
    );
  }
}
